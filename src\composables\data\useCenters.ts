/**
 * @fileoverview Composable for managing centers from API
 * @description Provides reactive centers data and searching functionality
 */

import { ref, computed, onMounted } from 'vue'
import { centerService, type CenterDto, type CenterOption, type CenterFilters } from '@/services/center.service'

/**
 * Composable for centers management
 */
export function useCenters() {
  // Reactive state
  const centers = ref<CenterDto[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const searchQuery = ref('')
  const filters = ref<CenterFilters>({})

  /**
   * Load centers from API
   */
  const loadCenters = async (forceRefresh: boolean = false) => {
    isLoading.value = true
    error.value = null
    
    try {
      centers.value = await centerService.fetchCenters(forceRefresh)
      console.log('✅ useCenters: Centers loaded from API:', centers.value.length, 'centers')
    } catch (err) {
      error.value = 'Failed to load centers'
      console.error('❌ useCenters: Error loading centers from API:', err)
      centers.value = []
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Get active centers only
   */
  const activeCenters = computed(() => 
    centers.value.filter(center => center.isActive)
  )

  /**
   * Get filtered centers based on current filters and search
   */
  const filteredCenters = computed(() => {
    let result = filters.value.activeOnly !== false ? activeCenters.value : centers.value

    // Apply exam type filter
    if (filters.value.examTypeId) {
      result = result.filter(center => center.examTypeId === filters.value.examTypeId)
    }

    // Apply school filter
    if (filters.value.schoolId) {
      result = result.filter(center => center.schoolId === filters.value.schoolId)
    }

    // Apply search query
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      result = result.filter(center =>
        center.manebcentreNo.toLowerCase().includes(query) ||
        center.id.toLowerCase().includes(query) ||
        (center.emiscentreNumber && center.emiscentreNumber.toLowerCase().includes(query))
      )
    }

    return result
  })

  /**
   * Get centers as dropdown options
   */
  const centerOptions = computed((): CenterOption[] => 
    filteredCenters.value.map(center => ({
      id: center.id,
      name: `Center ${center.manebcentreNo}`,
      centerNumber: center.manebcentreNo,
      examTypeId: center.examTypeId,
      schoolId: center.schoolId,
      isActive: center.isActive
    }))
  )

  /**
   * Get unique exam types from centers
   */
  const examTypes = computed(() => {
    const types = [...new Set(activeCenters.value.map(center => center.examTypeId))]
    return types.sort()
  })

  /**
   * Get unique schools from centers
   */
  const schools = computed(() => {
    const schoolIds = [...new Set(activeCenters.value.map(center => center.schoolId))]
    return schoolIds.sort()
  })

  /**
   * Search centers
   */
  const searchCenters = (query: string) => {
    searchQuery.value = query
  }

  /**
   * Set filters
   */
  const setFilters = (newFilters: Partial<CenterFilters>) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  /**
   * Clear filters
   */
  const clearFilters = () => {
    filters.value = {}
    searchQuery.value = ''
  }

  /**
   * Get center by ID
   */
  const getCenterById = (id: string): CenterDto | undefined => {
    return centers.value.find(center => center.id === id)
  }

  /**
   * Get centers by exam type
   */
  const getCentersByExamType = (examTypeId: string): CenterDto[] => {
    return activeCenters.value.filter(center => center.examTypeId === examTypeId)
  }

  /**
   * Get centers by school
   */
  const getCentersBySchool = (schoolId: string): CenterDto[] => {
    return activeCenters.value.filter(center => center.schoolId === schoolId)
  }

  /**
   * Get center name by ID
   */
  const getCenterName = (id: string): string => {
    const center = getCenterById(id)
    return center ? `Center ${center.manebcentreNo}` : id
  }

  /**
   * Refresh centers data
   */
  const refresh = () => loadCenters(true)

  /**
   * Clear error state
   */
  const clearError = () => {
    error.value = null
  }

  return {
    // State
    centers,
    activeCenters,
    filteredCenters,
    centerOptions,
    examTypes,
    schools,
    isLoading,
    error,
    searchQuery,
    filters,
    
    // Methods
    loadCenters,
    searchCenters,
    setFilters,
    clearFilters,
    getCenterById,
    getCentersByExamType,
    getCentersBySchool,
    getCenterName,
    refresh,
    clearError
  }
}

/**
 * Composable that auto-loads centers on mount
 */
export function useCentersAutoLoad() {
  const centersComposable = useCenters()
  
  onMounted(async () => {
    await centersComposable.loadCenters()
  })
  
  return centersComposable
}

/**
 * Composable for searchable center dropdown
 */
export function useSearchableCenters(initialFilters?: CenterFilters) {
  const centersComposable = useCentersAutoLoad()
  
  // Apply initial filters if provided
  if (initialFilters) {
    centersComposable.setFilters(initialFilters)
  }
  
  return {
    ...centersComposable,
    // Additional methods for searchable dropdown
    searchOptions: centersComposable.centerOptions,
    search: centersComposable.searchCenters,
    isSearching: centersComposable.isLoading
  }
}

/**
 * Composable for exam-type filtered centers
 */
export function useCentersByExamType(examTypeId: string) {
  const centersComposable = useCentersAutoLoad()
  
  // Set exam type filter
  centersComposable.setFilters({ examTypeId, activeOnly: true })
  
  return {
    ...centersComposable,
    examTypeCenters: centersComposable.filteredCenters
  }
}

/**
 * Composable for school filtered centers
 */
export function useCentersBySchool(schoolId: string) {
  const centersComposable = useCentersAutoLoad()

  // Set school filter
  centersComposable.setFilters({ schoolId, activeOnly: true })

  return {
    ...centersComposable,
    schoolCenters: centersComposable.filteredCenters
  }
}
