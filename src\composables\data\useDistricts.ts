/**
 * @fileoverview Composable for managing districts from API
 * @description Provides reactive districts data and loading functionality
 */

import { ref, computed, onMounted } from 'vue'
import { districtService, type DistrictDto, type DistrictOption } from '@/services/district.service'

/**
 * Composable for districts management
 */
export function useDistricts() {
  // Reactive state
  const districts = ref<DistrictDto[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  /**
   * Load districts from API
   */
  const loadDistricts = async (forceRefresh: boolean = false) => {
    isLoading.value = true
    error.value = null
    
    try {
      districts.value = await districtService.fetchDistricts(forceRefresh)
      console.log('✅ useDistricts: Districts loaded from API:', districts.value.length, 'districts')
    } catch (err) {
      error.value = 'Failed to load districts'
      console.error('❌ useDistricts: Error loading districts from API:', err)
      districts.value = []
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Get districts as dropdown options
   */
  const districtOptions = computed((): DistrictOption[] => 
    districts.value.map(district => ({
      id: district.id,
      name: district.name,
      code: district.geographicalDistrictId || district.id
    }))
  )

  /**
   * Get district by ID
   */
  const getDistrictById = (id: string): DistrictDto | undefined => {
    return districts.value.find(district => district.id === id)
  }

  /**
   * Get district name by ID
   */
  const getDistrictName = (id: string): string => {
    const district = getDistrictById(id)
    return district?.name || id
  }

  /**
   * Search districts by name
   */
  const searchDistricts = (query: string): DistrictDto[] => {
    const lowerQuery = query.toLowerCase()
    
    return districts.value.filter(district => 
      district.name.toLowerCase().includes(lowerQuery) ||
      district.geographicalDistrictId.toLowerCase().includes(lowerQuery)
    )
  }

  /**
   * Get districts formatted for filter options
   */
  const getFilterOptions = (parentId?: string): Array<{id: string, name: string, parentId?: string}> => {
    return districts.value.map(district => ({
      id: district.id,
      name: district.name,
      parentId
    }))
  }

  /**
   * Refresh districts data
   */
  const refresh = () => loadDistricts(true)

  /**
   * Clear error state
   */
  const clearError = () => {
    error.value = null
  }

  return {
    // State
    districts,
    districtOptions,
    isLoading,
    error,
    
    // Methods
    loadDistricts,
    getDistrictById,
    getDistrictName,
    searchDistricts,
    getFilterOptions,
    refresh,
    clearError
  }
}

/**
 * Composable that auto-loads districts on mount
 */
export function useDistrictsAutoLoad() {
  const districtsComposable = useDistricts()
  
  onMounted(async () => {
    await districtsComposable.loadDistricts()
  })
  
  return districtsComposable
}
