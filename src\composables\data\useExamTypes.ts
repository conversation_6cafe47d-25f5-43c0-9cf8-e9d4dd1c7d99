/**
 * @fileoverview Composable for managing exam types from API
 * @description Provides reactive exam types data and loading functionality
 */

import { ref, computed, onMounted } from 'vue'
import { examTypeService, type ExamTypeDto, type ExamTypeOption } from '@/services/exam-type.service'
import type { ExamTypeInfo } from '@/utils/exam-types'

/**
 * Composable for exam types management
 */
export function useExamTypes() {
  // Reactive state
  const examTypes = ref<ExamTypeDto[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  /**
   * Load exam types from API
   */
  const loadExamTypes = async (forceRefresh: boolean = false) => {
    isLoading.value = true
    error.value = null
    
    try {
      examTypes.value = await examTypeService.fetchExamTypes(forceRefresh)
      console.log('✅ useExamTypes: Exam types loaded from API:', examTypes.value)
    } catch (err) {
      error.value = 'Failed to load exam types'
      console.error('❌ useExamTypes: Error loading exam types from API:', err)
      examTypes.value = []
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Get active exam types only
   */
  const activeExamTypes = computed(() => 
    examTypes.value.filter(type => type.isActive)
  )

  /**
   * Get exam types as dropdown options
   */
  const examTypeOptions = computed((): ExamTypeOption[] => 
    activeExamTypes.value.map(type => ({
      value: type.id,
      label: type.id,
      description: type.name,
      isActive: type.isActive
    }))
  )

  /**
   * Get exam types in local format for backward compatibility
   */
  const examTypesLocal = computed((): ExamTypeInfo[] => 
    activeExamTypes.value.map(type => ({
      value: type.id,
      label: type.id,
      description: type.name,
      isActive: type.isActive,
      displayOrder: type.numericId
    }))
  )

  /**
   * Get exam type by ID
   */
  const getExamTypeById = (id: string): ExamTypeDto | undefined => {
    return examTypes.value.find(type => type.id === id)
  }

  /**
   * Get exam type description by ID
   */
  const getExamTypeDescription = (id: string): string => {
    const examType = getExamTypeById(id)
    return examType?.name || id
  }

  /**
   * Check if exam type ID is valid
   */
  const isValidExamType = (id: string): boolean => {
    const examType = getExamTypeById(id)
    return !!examType && examType.isActive
  }

  /**
   * Refresh exam types data
   */
  const refresh = () => loadExamTypes(true)

  /**
   * Clear error state
   */
  const clearError = () => {
    error.value = null
  }

  return {
    // State
    examTypes: examTypes.value,
    activeExamTypes,
    examTypeOptions,
    examTypesLocal,
    isLoading,
    error,
    
    // Methods
    loadExamTypes,
    getExamTypeById,
    getExamTypeDescription,
    isValidExamType,
    refresh,
    clearError
  }
}

/**
 * Composable that auto-loads exam types on mount
 */
export function useExamTypesAutoLoad() {
  const examTypesComposable = useExamTypes()
  
  onMounted(async () => {
    await examTypesComposable.loadExamTypes()
  })
  
  return examTypesComposable
}
