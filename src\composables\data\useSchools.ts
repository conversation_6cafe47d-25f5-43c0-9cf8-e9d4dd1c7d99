/**
 * @fileoverview Composable for managing schools from API
 * @description Provides reactive schools data and searching functionality
 */

import { ref, computed, onMounted } from 'vue'
import { schoolService, type SchoolDto, type SchoolOption, type SchoolFilters } from '@/services/school.service'

/**
 * Composable for schools management
 */
export function useSchools() {
  // Reactive state
  const schools = ref<SchoolDto[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const searchQuery = ref('')
  const filters = ref<SchoolFilters>({})

  /**
   * Load schools from API
   */
  const loadSchools = async (forceRefresh: boolean = false) => {
    isLoading.value = true
    error.value = null
    
    try {
      schools.value = await schoolService.fetchSchools(forceRefresh)
      console.log('✅ useSchools: Schools loaded from API:', schools.value.length, 'schools')
    } catch (err) {
      error.value = 'Failed to load schools'
      console.error('❌ useSchools: Error loading schools from API:', err)
      schools.value = []
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Get active schools only
   */
  const activeSchools = computed(() => 
    schools.value.filter(school => school.isActive)
  )

  /**
   * Get filtered schools based on current filters and search
   */
  const filteredSchools = computed(() => {
    let result = filters.value.activeOnly !== false ? activeSchools.value : schools.value

    // Apply district filter
    if (filters.value.districtId) {
      result = result.filter(school => school.examDistrictPartitionId === filters.value.districtId)
    }

    // Apply search query
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      result = result.filter(school =>
        school.fullname.toLowerCase().includes(query) ||
        school.shortName.toLowerCase().includes(query) ||
        school.id.toLowerCase().includes(query) ||
        (school.contactPerson && school.contactPerson.toLowerCase().includes(query))
      )
    }

    return result
  })

  /**
   * Get schools as dropdown options
   */
  const schoolOptions = computed((): SchoolOption[] => 
    filteredSchools.value.map(school => ({
      id: school.id,
      name: school.fullname,
      shortName: school.shortName,
      districtId: school.examDistrictPartitionId,
      contactPerson: school.contactPerson,
      phoneNumber: school.phoneNumber,
      isActive: school.isActive
    }))
  )

  /**
   * Get unique districts from schools
   */
  const districts = computed(() => {
    const districtIds = [...new Set(activeSchools.value.map(school => school.examDistrictPartitionId))]
    return districtIds.sort()
  })

  /**
   * Search schools
   */
  const searchSchools = (query: string) => {
    searchQuery.value = query
  }

  /**
   * Set filters
   */
  const setFilters = (newFilters: Partial<SchoolFilters>) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  /**
   * Clear filters
   */
  const clearFilters = () => {
    filters.value = {}
    searchQuery.value = ''
  }

  /**
   * Get school by ID
   */
  const getSchoolById = (id: string): SchoolDto | undefined => {
    return schools.value.find(school => school.id === id)
  }

  /**
   * Get schools by district
   */
  const getSchoolsByDistrict = (districtId: string): SchoolDto[] => {
    return activeSchools.value.filter(school => school.examDistrictPartitionId === districtId)
  }

  /**
   * Get school name by ID
   */
  const getSchoolName = (id: string): string => {
    const school = getSchoolById(id)
    return school?.fullname || id
  }

  /**
   * Refresh schools data
   */
  const refresh = () => loadSchools(true)

  /**
   * Clear error state
   */
  const clearError = () => {
    error.value = null
  }

  return {
    // State
    schools,
    activeSchools,
    filteredSchools,
    schoolOptions,
    districts,
    isLoading,
    error,
    searchQuery,
    filters,
    
    // Methods
    loadSchools,
    searchSchools,
    setFilters,
    clearFilters,
    getSchoolById,
    getSchoolsByDistrict,
    getSchoolName,
    refresh,
    clearError
  }
}

/**
 * Composable that auto-loads schools on mount
 */
export function useSchoolsAutoLoad() {
  const schoolsComposable = useSchools()
  
  onMounted(async () => {
    await schoolsComposable.loadSchools()
  })
  
  return schoolsComposable
}

/**
 * Composable for searchable school dropdown
 */
export function useSearchableSchools(initialFilters?: SchoolFilters) {
  const schoolsComposable = useSchoolsAutoLoad()
  
  // Apply initial filters if provided
  if (initialFilters) {
    schoolsComposable.setFilters(initialFilters)
  }
  
  return {
    ...schoolsComposable,
    // Additional methods for searchable dropdown
    searchOptions: schoolsComposable.schoolOptions,
    search: schoolsComposable.searchSchools,
    isSearching: schoolsComposable.isLoading
  }
}

/**
 * Composable for district filtered schools
 */
export function useSchoolsByDistrict(districtId: string) {
  const schoolsComposable = useSchoolsAutoLoad()
  
  // Set district filter
  schoolsComposable.setFilters({ districtId, activeOnly: true })
  
  return {
    ...schoolsComposable,
    districtSchools: schoolsComposable.filteredSchools
  }
}
