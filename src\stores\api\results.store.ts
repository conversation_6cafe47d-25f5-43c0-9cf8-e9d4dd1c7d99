import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { resultsService } from '@/services/results.service';
import type {
  StudentResultDto,
  ResultsFilterDto,
  ExaminationResultDto,
  ExaminationResultsFilterDto,
  StudentSubjectResult,
  StudentPaperResult,
  GradeLevel,
  GradeBoundaryDto,
  SubjectDto,
  PaperDto,
  StudentCertificateDto
} from '@/interfaces';

export const useResultsStore = defineStore('results', () => {
  // State
  const results = ref<StudentResultDto[]>([]);
  const examinationResults = ref<ExaminationResultDto[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  // Filters
  const filters = ref<ResultsFilterDto>({
    searchQuery: '',
    year: 'All',
    subjectId: 'All',
    gradeLevel: 'All',
    passStatus: 'All'
  });

  // Examination-level filters
  const examinationFilters = ref<ExaminationResultsFilterDto>({
    searchQuery: '',
    year: 'All',
    level: 'All',
    gradeLevel: 'All',
    passStatus: 'All',
    status: 'All'
  });

  // Statistics
  const statistics = ref({
    totalResults: 0,
    passCount: 0,
    failCount: 0,
    gradeDistribution: {
      'A': 0,
      'B': 0,
      'C': 0,
      'D': 0,
      'F': 0
    } as { [key in GradeLevel]: number },
    passRate: 0
  });

  // Computed
  const filteredResults = computed(() => {
    let filtered = results.value;

    // Apply search filter
    if (filters.value.searchQuery) {
      const query = filters.value.searchQuery.toLowerCase();
      filtered = filtered.filter(result => 
        result.studentId.toLowerCase().includes(query) ||
        result.calculatedGrade.toLowerCase().includes(query)
      );
    }

    // Apply year filter
    if (filters.value.year && filters.value.year !== 'All') {
      filtered = filtered.filter(result => result.year === filters.value.year);
    }

    // Apply subject filter
    if (filters.value.subjectId && filters.value.subjectId !== 'All') {
      filtered = filtered.filter(result => result.subjectId === filters.value.subjectId);
    }

    // Paper filter removed - system is now subject-based

    // Apply grade filter
    if (filters.value.gradeLevel && filters.value.gradeLevel !== 'All') {
      filtered = filtered.filter(result => result.calculatedGrade === filters.value.gradeLevel);
    }

    // Apply pass status filter
    if (filters.value.passStatus !== undefined && filters.value.passStatus !== 'All') {
      filtered = filtered.filter(result => result.passStatus === filters.value.passStatus);
    }

    return filtered.sort((a, b) => {
      // Sort by year (desc), then by student ID, then by subject
      if (a.year !== b.year) return b.year - a.year;
      if (a.studentId !== b.studentId) return a.studentId.localeCompare(b.studentId);
      return a.subjectId.localeCompare(b.subjectId);
    });
  });

  const availableYears = computed(() => {
    const years = [...new Set(results.value.map(r => r.year))].sort((a, b) => b - a);
    return years;
  });

  const availablePapers = computed(() => {
    // Papers removed - system is now subject-based
    return [];
  });

  // Examination-level computed properties
  const filteredExaminationResults = computed(() => {
    let filtered = examinationResults.value;

    // Apply search filter
    if (examinationFilters.value.searchQuery) {
      const query = examinationFilters.value.searchQuery.toLowerCase();
      filtered = filtered.filter(result =>
        result.studentId.toLowerCase().includes(query) ||
        (result.examNumber && result.examNumber.toLowerCase().includes(query))
      );
    }

    // Apply year filter
    if (examinationFilters.value.year && examinationFilters.value.year !== 'All') {
      filtered = filtered.filter(result => result.year === examinationFilters.value.year);
    }

    // Apply level filter
    if (examinationFilters.value.level && examinationFilters.value.level !== 'All') {
      filtered = filtered.filter(result => result.level === examinationFilters.value.level);
    }

    // Apply grade filter
    if (examinationFilters.value.gradeLevel && examinationFilters.value.gradeLevel !== 'All') {
      filtered = filtered.filter(result => result.averageGrade === examinationFilters.value.gradeLevel);
    }

    // Apply pass status filter
    if (examinationFilters.value.passStatus !== undefined && examinationFilters.value.passStatus !== 'All') {
      filtered = filtered.filter(result => {
        const passStatus = result.passRate >= 50; // Consider 50% pass rate as overall pass
        return passStatus === examinationFilters.value.passStatus;
      });
    }

    // Apply status filter
    if (examinationFilters.value.status && examinationFilters.value.status !== 'All') {
      filtered = filtered.filter(result => result.status === examinationFilters.value.status);
    }

    return filtered.sort((a, b) => {
      // Sort by year (desc), then by student ID
      if (a.year !== b.year) return b.year - a.year;
      return a.studentId.localeCompare(b.studentId);
    });
  });

  const availableExaminationYears = computed(() => {
    const years = [...new Set(examinationResults.value.map(r => r.year))].sort((a, b) => b - a);
    return years;
  });

  const availableLevels = computed(() => {
    const levels = [...new Set(examinationResults.value.map(r => r.level))];
    return levels;
  });

  // Actions
  const clearError = () => {
    error.value = null;
  };

  const resetFilters = () => {
    filters.value = {
      searchQuery: '',
      year: 'All',
      subjectId: 'All',
      gradeLevel: 'All',
      passStatus: 'All'
    };
  };

  const resetExaminationFilters = () => {
    examinationFilters.value = {
      searchQuery: '',
      year: 'All',
      level: 'All',
      gradeLevel: 'All',
      passStatus: 'All',
      status: 'All'
    };
  };

  const generateResults = async (
    gradeBoundaries: GradeBoundaryDto[],
    subjects: SubjectDto[],
    papers: PaperDto[]
  ) => {
    isLoading.value = true;
    error.value = null;
    try {
      results.value = await resultsService.generateResults(gradeBoundaries, subjects, papers);
      await generateExaminationResults(gradeBoundaries, subjects, papers);
      await updateStatistics(gradeBoundaries, subjects, papers);
    } catch (err: any) {
      error.value = err.message || 'Failed to generate results';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const generateExaminationResults = async (
    gradeBoundaries: GradeBoundaryDto[],
    subjects: SubjectDto[],
    papers: PaperDto[]
  ) => {
    try {
      // Group results by student and year to create examination-level results
      const studentExamMap = new Map<string, ExaminationResultDto>();

      for (const result of results.value) {
        const key = `${result.studentId}-${result.year}`;

        if (!studentExamMap.has(key)) {
          // Create new examination result
          const examResult: ExaminationResultDto = {
            studentId: result.studentId,
            examNumber: `${result.year}${result.studentId.slice(-3)}`, // Generate exam number
            level: result.year >= 2020 ? 'MSCE' : 'PLCE', // Simple logic for demo
            year: result.year,
            averageScore: 0,
            averageGrade: 'F',
            totalSubjects: 0,
            passedSubjects: 0,
            failedSubjects: 0,
            passRate: 0,
            status: 'Complete',
            subjects: []
          };
          studentExamMap.set(key, examResult);
        }

        const examResult = studentExamMap.get(key)!;

        // Find or create subject result
        let subjectResult = examResult.subjects.find(s => s.subjectId === result.subjectId);
        if (!subjectResult) {
          const subject = subjects.find(s => s.id === result.subjectId);
          subjectResult = {
            subjectId: result.subjectId,
            subjectName: subject?.name || 'Unknown Subject',
            subjectCode: subject?.code || 'UNK',
            papers: [],
            averageScore: 0,
            overallGrade: 'F',
            passStatus: false
          };
          examResult.subjects.push(subjectResult);
        }

        // Add paper result (using subject as paper for compatibility)
        const subject = subjects.find(s => s.id === result.subjectId);
        const paperResult: StudentPaperResult = {
          paperId: subject?.code || result.subjectId, // Use subject code or fallback to subjectId
          paperName: subject?.name || 'Unknown Subject',
          score: result.score,
          grade: result.calculatedGrade,
          passStatus: result.passStatus,
          scoreType: 'final' // Default score type
        };
        subjectResult.papers.push(paperResult);
      }

      // Calculate averages and grades for each examination result
      for (const examResult of studentExamMap.values()) {
        let totalScore = 0;
        let totalPapers = 0;

        for (const subject of examResult.subjects) {
          // Calculate subject average
          const subjectTotal = subject.papers.reduce((sum, paper) => sum + paper.score, 0);
          subject.averageScore = subject.papers.length > 0 ? subjectTotal / subject.papers.length : 0;

          // Determine subject grade based on average score
          subject.overallGrade = calculateGradeFromScore(subject.averageScore);
          subject.passStatus = subject.averageScore >= 50; // Simple pass threshold

          totalScore += subject.averageScore;
          totalPapers += 1;

          if (subject.passStatus) {
            examResult.passedSubjects++;
          } else {
            examResult.failedSubjects++;
          }
        }

        examResult.totalSubjects = examResult.subjects.length;
        examResult.averageScore = totalPapers > 0 ? totalScore / totalPapers : 0;
        examResult.averageGrade = calculateGradeFromScore(examResult.averageScore);
        examResult.passRate = examResult.totalSubjects > 0 ?
          (examResult.passedSubjects / examResult.totalSubjects) * 100 : 0;
      }

      examinationResults.value = Array.from(studentExamMap.values());
    } catch (err: any) {
      console.error('Failed to generate examination results:', err);
      throw err;
    }
  };

  const calculateGradeFromScore = (score: number): GradeLevel => {
    if (score >= 80) return 'A';
    if (score >= 70) return 'B';
    if (score >= 60) return 'C';
    if (score >= 50) return 'D';
    return 'F';
  };

  const updateStatistics = async (
    gradeBoundaries: GradeBoundaryDto[],
    subjects: SubjectDto[],
    papers: PaperDto[]
  ) => {
    try {
      statistics.value = await resultsService.getResultStatistics(gradeBoundaries, subjects, papers);
    } catch (err: any) {
      console.error('Failed to update statistics:', err);
    }
  };

  const generateCertificate = async (
    studentId: string,
    year: number,
    gradeBoundaries: GradeBoundaryDto[],
    subjects: SubjectDto[],
    papers: PaperDto[]
  ): Promise<StudentCertificateDto> => {
    isLoading.value = true;
    error.value = null;
    try {
      const certificate = await resultsService.generateCertificate(
        studentId,
        year,
        gradeBoundaries,
        subjects,
        papers
      );
      return certificate;
    } catch (err: any) {
      error.value = err.message || 'Failed to generate certificate';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const refreshResults = async (
    gradeBoundaries: GradeBoundaryDto[],
    subjects: SubjectDto[],
    papers: PaperDto[]
  ) => {
    await generateResults(gradeBoundaries, subjects, papers);
  };

  // Helper methods for getting names
  const getSubjectName = (subjectId: string, subjects: SubjectDto[]): string => {
    const subject = subjects.find(s => s.id === subjectId);
    return subject?.name || 'Unknown Subject';
  };

  const getPaperName = (paperId: string, papers: PaperDto[]): string => {
    // In subject-based system, paperId is actually subjectId
    const paper = papers.find(p => p.id === paperId);
    return paper?.name || 'Unknown Paper';
  };

  const getGradeBadgeClass = (grade: string): string => {
    const classes = {
      'A': 'bg-green-100 text-green-800',
      'B': 'bg-blue-100 text-blue-800',
      'C': 'bg-yellow-100 text-yellow-800',
      'D': 'bg-orange-100 text-orange-800',
      'F': 'bg-red-100 text-red-800'
    };
    return classes[grade as keyof typeof classes] || 'bg-gray-100 text-gray-800';
  };

  return {
    // State
    results,
    examinationResults,
    isLoading,
    error,
    filters,
    examinationFilters,
    statistics,

    // Getters
    filteredResults,
    filteredExaminationResults,
    availableYears,
    availableExaminationYears,
    availableLevels,
    availablePapers,

    // Actions
    clearError,
    resetFilters,
    resetExaminationFilters,
    generateResults,
    generateExaminationResults,
    updateStatistics,
    generateCertificate,
    refreshResults,

    // Helper methods
    getSubjectName,
    getPaperName,
    getGradeBadgeClass,
    calculateGradeFromScore,
  };
});
