import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { districtService } from '@/services/district.service'
import type { DistrictDto } from '@/services/district.service'
import { useAuthStore } from '../auth/auth.store'

export const useDistrictStore = defineStore('district', () => {
  // State
  const districts = ref<DistrictDto[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const districtsLoaded = ref(false)

  // Getters
  const getDistrictById = computed(() => {
    return (id: string) => districts.value.find(district => district.id === id)
  })

  const getActiveDistricts = computed(() => {
    return districts.value.filter(district => district.isActive !== false)
  })

  const hasError = computed(() => !!error.value)

  const getDistrictName = computed(() => {
    return (districtId: string) => {
      const district = getDistrictById.value(districtId)
      return district ? district.name : ''
    }
  })

  // Actions
  const clearError = () => {
    error.value = null
  }

  const clearDistricts = () => {
    districts.value = []
    districtsLoaded.value = false
  }

  /**
   * Fetch all districts
   */
  const fetchDistricts = async (filters: {
    pageSize?: number;
    search?: string;
  } = {}) => {
    // Return cached data if already loaded
    if (districtsLoaded.value && districts.value.length > 0) {
      console.log('✅ Districts loaded from cache:', districts.value.length, 'items')
      return districts.value
    }

    isLoading.value = true
    error.value = null
    
    try {
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated) {
        throw new Error('Authentication required')
      }

      console.log('🔍 Fetching districts with filters:', filters)
      const response = await districtService.fetchDistricts(filters)
      
      districts.value = response.items
      districtsLoaded.value = true
      
      console.log('✅ Districts loaded from API:', response.items.length, 'items')
      return response.items
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch districts'
      console.error('❌ Error fetching districts:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Search districts by name
   */
  const searchDistricts = async (query: string) => {
    try {
      console.log('🔍 Searching districts with query:', query)
      
      const filters = {
        search: query,
        pageSize: 100
      }
      
      const response = await districtService.fetchDistricts(filters)
      console.log('✅ District search results:', response.items.length, 'districts')
      return response.items
    } catch (err: any) {
      error.value = err.message || 'Failed to search districts'
      console.error('❌ Error searching districts:', err)
      throw err
    }
  }

  /**
   * Get district options for dropdowns
   */
  const getDistrictOptions = () => {
    return getActiveDistricts.value.map(district => ({
      id: district.id,
      name: district.name,
      description: district.description || '',
      value: district.id
    }))
  }

  /**
   * Load districts if not already loaded
   */
  const ensureDistrictsLoaded = async () => {
    if (!districtsLoaded.value) {
      await fetchDistricts({ pageSize: 100 })
    }
    return districts.value
  }

  /**
   * Reset store state
   */
  const resetStore = () => {
    districts.value = []
    isLoading.value = false
    error.value = null
    districtsLoaded.value = false
  }

  return {
    // State
    districts,
    isLoading,
    error,
    districtsLoaded,
    
    // Getters
    getDistrictById,
    getActiveDistricts,
    getDistrictName,
    hasError,
    
    // Actions
    clearError,
    clearDistricts,
    fetchDistricts,
    searchDistricts,
    getDistrictOptions,
    ensureDistrictsLoaded,
    resetStore
  }
})
