import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { schoolService } from '@/services/school.service'
import type { SchoolDto } from '@/services/school.service'
import { useAuthStore } from '../auth/auth.store'

export const useSchoolStore = defineStore('school', () => {
  // State
  const schools = ref<SchoolDto[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const schoolsCache = ref(new Map<string, SchoolDto[]>())
  const schoolsLoaded = ref(false)

  // Getters
  const getSchoolById = computed(() => {
    return (id: string) => schools.value.find(school => school.id === id)
  })

  const getSchoolsByDistrict = computed(() => {
    return (districtId: string) => schools.value.filter(school => 
      school.examDistrictPartitionId === districtId
    )
  })

  const getActiveSchools = computed(() => {
    return schools.value.filter(school => school.isActive !== false)
  })

  const hasError = computed(() => !!error.value)

  const getSchoolName = computed(() => {
    return (schoolId: string) => {
      const school = getSchoolById.value(schoolId)
      return school ? (school.fullname || school.shortName) : ''
    }
  })

  // Actions
  const clearError = () => {
    error.value = null
  }

  const clearSchools = () => {
    schools.value = []
    schoolsLoaded.value = false
  }

  const clearCache = () => {
    schoolsCache.value.clear()
  }

  /**
   * Fetch all schools with optional filtering
   */
  const fetchSchools = async (filters: {
    pageSize?: number;
    search?: string;
    districtId?: string;
  } = {}) => {
    isLoading.value = true
    error.value = null
    
    try {
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated) {
        throw new Error('Authentication required')
      }

      console.log('🔍 Fetching schools with filters:', filters)
      const response = await schoolService.fetchSchools(filters)
      
      schools.value = response.items
      schoolsLoaded.value = true
      
      console.log('✅ Schools loaded:', response.items.length, 'items')
      return response
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch schools'
      console.error('❌ Error fetching schools:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Load schools for a specific district
   */
  const loadSchoolsForDistrict = async (districtId: string) => {
    if (!districtId) {
      console.warn('⚠️ No district ID provided for school loading')
      return []
    }

    // Check cache first
    const cacheKey = `district_${districtId}`
    if (schoolsCache.value.has(cacheKey)) {
      const cachedSchools = schoolsCache.value.get(cacheKey)!
      schools.value = cachedSchools
      schoolsLoaded.value = true
      console.log('✅ Schools loaded from cache for district:', districtId, cachedSchools.length, 'schools')
      return cachedSchools
    }

    isLoading.value = true
    error.value = null
    
    try {
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated) {
        throw new Error('Authentication required')
      }

      console.log('🔍 Loading schools for district:', districtId)
      
      const filters = {
        pageSize: 500, // Load more schools for districts
        districtId
      }
      
      const response = await schoolService.fetchSchools(filters)
      
      // Filter by district on client side as well for extra safety
      const districtSchools = response.items.filter(school => 
        school.examDistrictPartitionId === districtId
      )
      
      schools.value = districtSchools
      schoolsLoaded.value = true
      schoolsCache.value.set(cacheKey, districtSchools)
      
      console.log('✅ Schools loaded for district:', districtId, districtSchools.length, 'schools')
      return districtSchools
    } catch (err: any) {
      error.value = err.message || 'Failed to load schools for district'
      console.error('❌ Error loading schools for district:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Search schools by name
   */
  const searchSchools = async (query: string, districtId?: string) => {
    try {
      console.log('🔍 Searching schools with query:', query, 'in district:', districtId)
      
      const filters: any = {
        search: query,
        pageSize: 100
      }
      
      if (districtId) {
        filters.districtId = districtId
      }
      
      const response = await schoolService.fetchSchools(filters)
      console.log('✅ School search results:', response.items.length, 'schools')
      return response.items
    } catch (err: any) {
      error.value = err.message || 'Failed to search schools'
      console.error('❌ Error searching schools:', err)
      throw err
    }
  }

  /**
   * Get school options for dropdowns
   */
  const getSchoolOptions = (districtId?: string) => {
    let filteredSchools = getActiveSchools.value
    
    if (districtId) {
      filteredSchools = getSchoolsByDistrict.value(districtId)
    }
    
    return filteredSchools.map(school => ({
      id: school.id,
      name: school.fullname || school.shortName,
      description: school.shortName || '',
      value: school.id
    }))
  }

  /**
   * Reset store state
   */
  const resetStore = () => {
    schools.value = []
    isLoading.value = false
    error.value = null
    schoolsCache.value.clear()
    schoolsLoaded.value = false
  }

  return {
    // State
    schools,
    isLoading,
    error,
    schoolsLoaded,
    
    // Getters
    getSchoolById,
    getSchoolsByDistrict,
    getActiveSchools,
    getSchoolName,
    hasError,
    
    // Actions
    clearError,
    clearSchools,
    clearCache,
    fetchSchools,
    loadSchoolsForDistrict,
    searchSchools,
    getSchoolOptions,
    resetStore
  }
})
