import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { subjectService } from '@/services/subject.service'
import type { SubjectDto } from '@/services/subject.service'
import { useAuthStore } from '../auth/auth.store'

export const useSubjectStore = defineStore('subject', () => {
  // State
  const subjects = ref<SubjectDto[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const subjectsCache = ref(new Map<string, SubjectDto[]>())

  // Getters
  const getSubjectById = computed(() => {
    return (id: string) => subjects.value.find(subject => subject.id === id)
  })

  const getSubjectsByExamType = computed(() => {
    return (examTypeId: string) => subjects.value.filter(subject => 
      subject.examTypeId === examTypeId
    )
  })

  const getActiveSubjects = computed(() => {
    return subjects.value.filter(subject => subject.isActive !== false)
  })

  const hasError = computed(() => !!error.value)

  const getSubjectName = computed(() => {
    return (subjectId: string) => {
      const subject = getSubjectById.value(subjectId)
      return subject ? subject.name : ''
    }
  })

  // Actions
  const clearError = () => {
    error.value = null
  }

  const clearSubjects = () => {
    subjects.value = []
  }

  const clearCache = () => {
    subjectsCache.value.clear()
  }

  /**
   * Fetch subjects with optional filtering
   */
  const fetchSubjects = async (filters: {
    examTypeId?: string;
    pageSize?: number;
    search?: string;
  } = {}) => {
    isLoading.value = true
    error.value = null
    
    try {
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated) {
        throw new Error('Authentication required')
      }

      // Create cache key for this filter combination
      const cacheKey = JSON.stringify(filters)
      
      // Check cache first
      if (subjectsCache.value.has(cacheKey)) {
        subjects.value = subjectsCache.value.get(cacheKey)!
        console.log('✅ Subjects loaded from cache:', subjects.value.length, 'items')
        return subjects.value
      }

      console.log('🔍 Fetching subjects with filters:', filters)
      const response = await subjectService.fetchSubjects(filters)
      
      subjects.value = response.items
      subjectsCache.value.set(cacheKey, response.items)
      
      console.log('✅ Subjects loaded from API:', response.items.length, 'items')
      return response.items
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch subjects'
      console.error('❌ Error fetching subjects:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Load subjects for a specific exam type
   */
  const loadSubjectsForExamType = async (examTypeId: string) => {
    if (!examTypeId) {
      console.warn('⚠️ No exam type ID provided for subject loading')
      return []
    }

    try {
      console.log('🔍 Loading subjects for exam type:', examTypeId)
      
      const filters = {
        examTypeId,
        pageSize: 200
      }
      
      const response = await fetchSubjects(filters)
      console.log('✅ Subjects loaded for exam type:', examTypeId, response.length, 'subjects')
      return response
    } catch (err: any) {
      console.error('❌ Error loading subjects for exam type:', err)
      throw err
    }
  }

  /**
   * Search subjects by name
   */
  const searchSubjects = async (query: string, examTypeId?: string) => {
    try {
      console.log('🔍 Searching subjects with query:', query, 'for exam type:', examTypeId)
      
      const filters: any = {
        search: query,
        pageSize: 100
      }
      
      if (examTypeId) {
        filters.examTypeId = examTypeId
      }
      
      const response = await fetchSubjects(filters)
      console.log('✅ Subject search results:', response.length, 'subjects')
      return response
    } catch (err: any) {
      error.value = err.message || 'Failed to search subjects'
      console.error('❌ Error searching subjects:', err)
      throw err
    }
  }

  /**
   * Get subject options for dropdowns
   */
  const getSubjectOptions = (examTypeId?: string) => {
    let filteredSubjects = getActiveSubjects.value
    
    if (examTypeId) {
      filteredSubjects = getSubjectsByExamType.value(examTypeId)
    }
    
    return filteredSubjects.map(subject => ({
      id: subject.id,
      name: subject.name,
      description: subject.description || '',
      value: subject.id
    }))
  }

  /**
   * Reset store state
   */
  const resetStore = () => {
    subjects.value = []
    isLoading.value = false
    error.value = null
    subjectsCache.value.clear()
  }

  return {
    // State
    subjects,
    isLoading,
    error,
    
    // Getters
    getSubjectById,
    getSubjectsByExamType,
    getActiveSubjects,
    getSubjectName,
    hasError,
    
    // Actions
    clearError,
    clearSubjects,
    clearCache,
    fetchSubjects,
    loadSubjectsForExamType,
    searchSubjects,
    getSubjectOptions,
    resetStore
  }
})
